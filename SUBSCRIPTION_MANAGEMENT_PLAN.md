# EMR Subscription Management System - Technical Plan & Estimation

## 📋 Executive Summary

This document outlines the technical implementation plan for a comprehensive subscription management system for the EMR platform. The system will support both organization-level and individual user subscriptions with feature-based access control, payment integration, and administrative management.

## 🎯 Requirements Overview

### Core Features
1. **Subscription Plan Management**: Create, edit, delete subscription plans with base and add-on features
2. **Feature Management**: Categorize features into EMR/MRD base features and add-ons
3. **Organization Subscriptions**: Assign plans to organizations with permission integration
4. **User Subscriptions**: Individual user subscriptions with dedicated organization
5. **Payment Integration**: Razorpay integration for subscription payments
6. **Permission Integration**: Dynamic permission assignment based on subscription features
7. **Administrative Tools**: Superadmin tools for plan management and organization creation
8. **Get Quote System**: Email-based quote request system

## 🏗️ Current System Architecture Analysis

### Existing Infrastructure
- **Backend**: Azure Functions (Node.js/JavaScript)
- **Database**: Azure Cosmos DB (NoSQL, container-based)
- **Authentication**: Azure B2C OAuth
- **Payment**: Razorpay (already configured)
- **Email**: Nodemailer with Gmail SMTP
- **Permission System**: Role-based with hierarchical permission keys

### Current Containers
- `Users` - User management with organization assignment
- `Organizations` - Organization data and settings
- `Roles` - Role definitions
- `sys_role_permissions` - Role-permission mappings
- `Payments` - Payment transactions and history

### Current Permission System
- **Permission Keys**: Hierarchical format (e.g., `emr.patientinfo.view`, `mrd.patient-queue.manage`)
- **Modules**: EMR (Electronic Medical Records) and MRD (Medical Records Department)
- **Role Assignment**: Organization-specific roles with permission mapping
- **API Integration**: Permission keys mapped to API endpoints and methods

## 📊 Database Schema Design

### New Containers Required

#### 1. `subscription_features` Container
```javascript
{
  "id": "feature-uuid",
  "key": "emr.patientinfo.view",
  "name": "Patient Information View",
  "description": "View patient demographic and basic information",
  "module": "EMR", // EMR or MRD
  "category": "BASE", // BASE or ADDON
  "permissionKeys": ["emr.patientinfo.view"],
  "isActive": true,
  "displayOrder": 1,
  "created_on": "2025-10-27T...",
  "updated_on": "2025-10-27T..."
}
```

#### 2. `subscription_plans` Container
```javascript
{
  "id": "plan-uuid",
  "name": "EMR Professional Plan",
  "description": "Complete EMR solution with advanced features",
  "planType": "ORGANIZATION", // ORGANIZATION or USER
  "baseFeatures": ["feature-uuid-1", "feature-uuid-2"],
  "addonFeatures": ["feature-uuid-3", "feature-uuid-4"],
  "pricing": {
    "monthly": 5000, // in paise
    "yearly": 50000,
    "currency": "INR"
  },
  "isActive": true,
  "maxUsers": 50, // for organization plans
  "created_on": "2025-10-27T...",
  "updated_on": "2025-10-27T..."
}
```

#### 3. `organization_subscriptions` Container
```javascript
{
  "id": "org-sub-uuid",
  "organizationId": "org-uuid",
  "planId": "plan-uuid",
  "status": "ACTIVE", // ACTIVE, INACTIVE, EXPIRED, CANCELLED
  "startDate": "2025-10-27T...",
  "endDate": "2026-10-27T...",
  "billingCycle": "MONTHLY", // MONTHLY, YEARLY
  "paymentStatus": "PAID", // PAID, PENDING, FAILED
  "lastPaymentId": "payment-uuid",
  "autoRenew": true,
  "created_on": "2025-10-27T...",
  "updated_on": "2025-10-27T..."
}
```

#### 4. `user_subscriptions` Container
```javascript
{
  "id": "user-sub-uuid",
  "userId": "user-uuid",
  "planId": "plan-uuid",
  "status": "ACTIVE",
  "startDate": "2025-10-27T...",
  "endDate": "2026-10-27T...",
  "billingCycle": "MONTHLY",
  "paymentStatus": "PAID",
  "lastPaymentId": "payment-uuid",
  "autoRenew": true,
  "subscriptionOrganizationId": "subscription-org-uuid",
  "created_on": "2025-10-27T...",
  "updated_on": "2025-10-27T..."
}
```

#### 5. `subscription_organizations` Container
```javascript
{
  "id": "subscription-org-uuid",
  "name": "Subscription Users Organization",
  "type": "SUBSCRIPTION_CONTAINER",
  "isActive": true,
  "created_on": "2025-10-27T...",
  "updated_on": "2025-10-27T..."
}
```

### Extended Existing Containers

#### Updated `Organizations` Container
```javascript
{
  // ... existing fields
  "subscriptionId": "org-sub-uuid", // Link to organization subscription
  "subscriptionStatus": "ACTIVE", // Quick access to subscription status
  "subscriptionPlanName": "EMR Professional Plan" // Cached for quick access
}
```

#### Updated `Users` Container
```javascript
{
  // ... existing fields
  "subscriptionId": "user-sub-uuid", // For individual user subscriptions
  "subscriptionStatus": "ACTIVE",
  "isSubscriptionUser": false // True for users created via subscription flow
}
```

## 🔧 API Specifications

### Subscription Features Management

#### 1. GET `/api/subscription/features`
**Purpose**: List all available subscription features
**Query Parameters**:
- `module` (optional): EMR or MRD
- `category` (optional): BASE or ADDON
- `isActive` (optional): true/false

**Response**:
```javascript
{
  "features": [
    {
      "id": "feature-uuid",
      "key": "emr.patientinfo.view",
      "name": "Patient Information View",
      "module": "EMR",
      "category": "BASE",
      "permissionKeys": ["emr.patientinfo.view"]
    }
  ],
  "totalCount": 25
}
```

#### 2. POST `/api/subscription/features`
**Purpose**: Create new subscription feature
**Body**:
```javascript
{
  "key": "emr.advanced.analytics",
  "name": "Advanced Analytics",
  "description": "Advanced reporting and analytics features",
  "module": "EMR",
  "category": "ADDON",
  "permissionKeys": ["emr.analytics.view", "emr.reports.advanced"]
}
```

### Subscription Plans Management

#### 3. GET `/api/subscription/plans`
**Purpose**: List subscription plans
**Query Parameters**:
- `planType` (optional): ORGANIZATION or USER
- `isActive` (optional): true/false

#### 4. POST `/api/subscription/plans`
**Purpose**: Create subscription plan
**Body**:
```javascript
{
  "name": "EMR Professional Plan",
  "description": "Complete EMR solution",
  "planType": "ORGANIZATION",
  "baseFeatures": ["feature-uuid-1", "feature-uuid-2"],
  "addonFeatures": ["feature-uuid-3"],
  "pricing": {
    "monthly": 5000,
    "yearly": 50000
  },
  "maxUsers": 50
}
```

#### 5. PATCH `/api/subscription/plans/{planId}`
**Purpose**: Update subscription plan

#### 6. DELETE `/api/subscription/plans/{planId}`
**Purpose**: Deactivate subscription plan (soft delete)

### Organization Subscription Management

#### 7. POST `/api/subscription/organization/assign`
**Purpose**: Assign subscription plan to organization
**Body**:
```javascript
{
  "organizationId": "org-uuid",
  "planId": "plan-uuid",
  "billingCycle": "MONTHLY",
  "startDate": "2025-10-27T..."
}
```

#### 8. GET `/api/subscription/organization/{organizationId}`
**Purpose**: Get organization subscription details

#### 9. PATCH `/api/subscription/organization/{subscriptionId}/upgrade`
**Purpose**: Upgrade organization subscription plan

### User Subscription Management

#### 10. POST `/api/subscription/user/signup`
**Purpose**: User subscription signup with payment
**Body**:
```javascript
{
  "planId": "plan-uuid",
  "userDetails": {
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "billingCycle": "MONTHLY",
  "paymentDetails": {
    "razorpay_payment_id": "pay_xxx",
    "razorpay_order_id": "order_xxx",
    "razorpay_signature": "signature_xxx"
  }
}
```

#### 11. GET `/api/subscription/user/plans`
**Purpose**: Get available user subscription plans

#### 12. POST `/api/subscription/user/upgrade`
**Purpose**: Upgrade user subscription

### Quote Management

#### 13. POST `/api/subscription/quote/request`
**Purpose**: Submit quote request
**Body**:
```javascript
{
  "organizationName": "ABC Hospital",
  "contactPerson": "Dr. Smith",
  "email": "<EMAIL>",
  "phone": "+91-9876543210",
  "requirements": "Need EMR system for 100 users",
  "estimatedUsers": 100,
  "modules": ["EMR", "MRD"]
}
```

### Administrative APIs

#### 14. POST `/api/admin/organization/create-with-plan`
**Purpose**: Superadmin creates organization with subscription plan
**Body**:
```javascript
{
  "organizationData": {
    "name": "ABC Hospital",
    "contactEmail": "<EMAIL>",
    "contactPersonName": "Dr. Smith"
  },
  "planId": "plan-uuid",
  "subscriptionDetails": {
    "billingCycle": "YEARLY",
    "startDate": "2025-10-27T..."
  }
}
```

## 🔐 Permission Integration Strategy

### Feature-to-Permission Mapping
Each subscription feature will contain an array of permission keys that should be granted when the feature is included in a subscription.

### Dynamic Permission Assignment
1. **Organization Subscriptions**: 
   - When a plan is assigned, update organization roles to include only permissions from subscribed features
   - Existing permission assignment UI will show subscribed features as enabled, others as disabled

2. **User Subscriptions**:
   - Create user-specific role with permissions from subscribed features
   - Assign user to subscription organization with limited permissions

### Permission Checking Flow
```javascript
// Enhanced permission check
async function checkUserPermissions(userId, requiredPermission) {
  const user = await getUserById(userId);
  
  if (user.isSubscriptionUser) {
    // Check user subscription permissions
    const subscription = await getUserSubscription(user.subscriptionId);
    const plan = await getSubscriptionPlan(subscription.planId);
    const allowedPermissions = await getPermissionsFromFeatures(plan.baseFeatures, plan.addonFeatures);
    return allowedPermissions.includes(requiredPermission);
  } else {
    // Check organization subscription permissions
    const org = await getOrganization(user.organizationId);
    if (org.subscriptionId) {
      const subscription = await getOrganizationSubscription(org.subscriptionId);
      const plan = await getSubscriptionPlan(subscription.planId);
      const allowedPermissions = await getPermissionsFromFeatures(plan.baseFeatures, plan.addonFeatures);
      return allowedPermissions.includes(requiredPermission);
    }
    // Fallback to existing role-based permissions
    return checkRoleBasedPermissions(user.roleId, requiredPermission);
  }
}
```

## 💳 Payment Integration

### Razorpay Integration Points
1. **User Subscription Payment**: Direct payment during signup
2. **Organization Subscription**: Admin-initiated payments
3. **Subscription Renewals**: Automated renewal payments
4. **Plan Upgrades**: Prorated payment calculations

### Payment Flow
1. Create Razorpay order with subscription metadata
2. Process payment verification
3. Activate subscription upon successful payment
4. Handle webhook notifications for payment status updates

## 📧 Email Integration

### Email Templates Required
1. **Subscription Welcome Email**: For new user subscriptions
2. **Organization Subscription Confirmation**: For organization plan assignments
3. **Quote Request Email**: To sales team
4. **Subscription Renewal Reminders**: Before expiration
5. **Payment Failure Notifications**: For failed payments

### Quote Request Email Template
```javascript
const quoteRequestTemplate = {
  to: process.env.SALES_TEAM_EMAIL,
  subject: `New Quote Request from ${organizationName}`,
  html: `
    <h2>New Quote Request</h2>
    <p><strong>Organization:</strong> ${organizationName}</p>
    <p><strong>Contact Person:</strong> ${contactPerson}</p>
    <p><strong>Email:</strong> ${email}</p>
    <p><strong>Phone:</strong> ${phone}</p>
    <p><strong>Estimated Users:</strong> ${estimatedUsers}</p>
    <p><strong>Required Modules:</strong> ${modules.join(', ')}</p>
    <p><strong>Requirements:</strong></p>
    <p>${requirements}</p>
  `
};
```

## ⏱️ Implementation Timeline & Estimates

### Phase 1: Foundation (Week 1-2) - 80 hours
**Database Schema & Models (20 hours)**
- Create new Cosmos DB containers
- Design and implement data models
- Set up container relationships

**Core Services Development (40 hours)**
- Subscription feature service
- Subscription plan service  
- Organization subscription service
- User subscription service

**Basic API Endpoints (20 hours)**
- Feature CRUD operations
- Plan CRUD operations
- Basic subscription assignment

### Phase 2: Permission Integration (Week 3) - 40 hours
**Permission System Enhancement (25 hours)**
- Modify existing permission checking logic
- Implement feature-to-permission mapping
- Update role assignment for subscriptions

**UI Permission Integration (15 hours)**
- Modify permission assignment UI
- Add subscription-based permission filtering
- Update role management screens

### Phase 3: Payment Integration (Week 4) - 35 hours
**Razorpay Integration (20 hours)**
- Subscription payment flows
- Payment verification for subscriptions
- Webhook handling for subscription payments

**User Subscription Flow (15 hours)**
- Complete user signup with payment
- Subscription organization creation
- User assignment to subscription organization

### Phase 4: Administrative Features (Week 5) - 30 hours
**Superadmin Tools (15 hours)**
- Organization creation with subscription
- Plan assignment interfaces
- Subscription management dashboard

**Quote System (15 hours)**
- Quote request API
- Email template implementation
- Sales team notification system

### Phase 5: Advanced Features (Week 6) - 35 hours
**Subscription Management (20 hours)**
- Plan upgrade/downgrade functionality
- Subscription renewal handling
- Billing cycle management

**Monitoring & Analytics (15 hours)**
- Subscription analytics
- Usage tracking
- Revenue reporting

### Phase 6: Testing & Documentation (Week 7) - 25 hours
**Testing (15 hours)**
- Unit tests for all services
- Integration tests for payment flows
- End-to-end subscription testing

**Documentation (10 hours)**
- API documentation
- User guides
- Administrative documentation

## 📊 Total Estimation Summary

| Phase | Duration | Hours | Key Deliverables |
|-------|----------|-------|------------------|
| Phase 1 | 2 weeks | 80 | Database schema, core services, basic APIs |
| Phase 2 | 1 week | 40 | Permission integration, UI updates |
| Phase 3 | 1 week | 35 | Payment integration, user subscription flow |
| Phase 4 | 1 week | 30 | Admin tools, quote system |
| Phase 5 | 1 week | 35 | Advanced features, subscription management |
| Phase 6 | 1 week | 25 | Testing, documentation |
| **Total** | **7 weeks** | **245 hours** | **Complete subscription system** |

## 🚀 Implementation Priority

### High Priority (Must Have)
1. Subscription plan creation and management
2. Organization subscription assignment
3. Permission integration with existing system
4. Basic payment integration
5. Quote request system

### Medium Priority (Should Have)
1. User subscription flow
2. Plan upgrade/downgrade
3. Subscription analytics
4. Automated renewal handling

### Low Priority (Nice to Have)
1. Advanced analytics and reporting
2. Subscription usage tracking
3. Multi-currency support
4. Advanced billing features

## 🔄 Migration Strategy

### Existing Organizations
1. Create default "Legacy" subscription plan with all current permissions
2. Assign legacy plan to all existing organizations
3. Gradually migrate organizations to appropriate subscription plans

### Existing Users
1. Maintain current role-based permissions for existing users
2. Add subscription checking as fallback to role-based permissions
3. Provide migration tools for converting to subscription-based permissions

## 📋 Success Metrics

### Technical Metrics
- API response times < 500ms
- 99.9% uptime for subscription services
- Zero data loss during subscription operations
- Successful payment processing rate > 98%

### Business Metrics
- Subscription conversion rate
- Average revenue per user (ARPU)
- Customer acquisition cost (CAC)
- Subscription renewal rate

## 🔧 Technical Considerations

### Scalability
- Container partitioning strategy for large subscription datasets
- Caching strategy for frequently accessed subscription data
- Batch processing for subscription renewals

### Security
- Secure payment data handling
- Subscription data encryption
- Access control for administrative functions

### Monitoring
- Subscription health monitoring
- Payment failure alerting
- Usage analytics and reporting

## 🛠️ Detailed Implementation Specifications

### Service Layer Architecture

#### 1. SubscriptionFeatureService
```javascript
class SubscriptionFeatureService {
  async createFeature(featureData) {
    // Validate permission keys exist in APIPermissions
    // Create feature with auto-generated UUID
    // Return created feature
  }

  async getFeaturesByModule(module, category = null) {
    // Query features by module (EMR/MRD) and optional category
    // Return paginated results
  }

  async updateFeature(featureId, updateData) {
    // Update feature with validation
    // Handle permission key changes
  }

  async getPermissionKeysFromFeatures(featureIds) {
    // Aggregate all permission keys from given features
    // Return unique permission keys array
  }
}
```

#### 2. SubscriptionPlanService
```javascript
class SubscriptionPlanService {
  async createPlan(planData) {
    // Validate base and addon features exist
    // Calculate total permissions
    // Create plan with pricing validation
  }

  async assignPlanToOrganization(organizationId, planId, billingCycle) {
    // Create organization subscription
    // Update organization permissions
    // Trigger permission refresh for all org users
  }

  async upgradePlan(subscriptionId, newPlanId) {
    // Calculate prorated charges
    // Update subscription
    // Refresh permissions
  }
}
```

#### 3. UserSubscriptionService
```javascript
class UserSubscriptionService {
  async createUserSubscription(planId, userDetails, paymentData) {
    // Verify payment with Razorpay
    // Create user in subscription organization
    // Create B2C user account
    // Send welcome email
    // Return subscription details
  }

  async getSubscriptionOrganization() {
    // Get or create the special subscription organization
    // Ensure it has proper configuration
  }
}
```

### Repository Layer

#### 1. SubscriptionFeatureRepository
```javascript
class SubscriptionFeatureRepository {
  async createFeature(feature) {
    return await cosmosDbContext.createItem(feature, 'subscription_features');
  }

  async getFeaturesByQuery(query, pageSize, continuationToken) {
    return await cosmosDbContext.getAllItemQuery(
      'subscription_features',
      query,
      pageSize,
      continuationToken
    );
  }

  async getFeaturesByIds(featureIds) {
    const query = `SELECT * FROM c WHERE c.id IN (${featureIds.map(id => `"${id}"`).join(',')})`;
    return await cosmosDbContext.queryItems(query, 'subscription_features');
  }
}
```

### Model Definitions

#### 1. SubscriptionFeatureModel
```javascript
class SubscriptionFeatureModel extends CosmosDbMetadata {
  constructor(data) {
    super(data);
    this.id = data.id || uuidv4();
    this.key = data.key || '';
    this.name = data.name || '';
    this.description = data.description || '';
    this.module = data.module || ''; // EMR or MRD
    this.category = data.category || ''; // BASE or ADDON
    this.permissionKeys = data.permissionKeys || [];
    this.isActive = data.isActive !== undefined ? data.isActive : true;
    this.displayOrder = data.displayOrder || 0;

    // Validation
    if (!['EMR', 'MRD'].includes(this.module)) {
      throw new Error('Module must be EMR or MRD');
    }
    if (!['BASE', 'ADDON'].includes(this.category)) {
      throw new Error('Category must be BASE or ADDON');
    }
  }
}
```

#### 2. SubscriptionPlanModel
```javascript
class SubscriptionPlanModel extends CosmosDbMetadata {
  constructor(data) {
    super(data);
    this.id = data.id || uuidv4();
    this.name = data.name || '';
    this.description = data.description || '';
    this.planType = data.planType || 'ORGANIZATION'; // ORGANIZATION or USER
    this.baseFeatures = data.baseFeatures || [];
    this.addonFeatures = data.addonFeatures || [];
    this.pricing = {
      monthly: data.pricing?.monthly || 0,
      yearly: data.pricing?.yearly || 0,
      currency: data.pricing?.currency || 'INR'
    };
    this.maxUsers = data.maxUsers || null;
    this.isActive = data.isActive !== undefined ? data.isActive : true;

    // Validation
    if (!['ORGANIZATION', 'USER'].includes(this.planType)) {
      throw new Error('Plan type must be ORGANIZATION or USER');
    }
    if (this.pricing.monthly < 0 || this.pricing.yearly < 0) {
      throw new Error('Pricing cannot be negative');
    }
  }
}
```

### Handler Implementations

#### 1. SubscriptionFeatureHandler
```javascript
class SubscriptionFeatureHandler {
  async createFeature(req) {
    try {
      const featureData = await req.json();

      // Validate required fields
      const requiredFields = ['key', 'name', 'module', 'category', 'permissionKeys'];
      const missingFields = requiredFields.filter(field => !featureData[field]);

      if (missingFields.length > 0) {
        return jsonResponse(
          `Missing required fields: ${missingFields.join(', ')}`,
          HttpStatusCode.BadRequest
        );
      }

      // Validate permission keys exist
      const { APIPermissions } = require('../common/permissions');
      const invalidKeys = featureData.permissionKeys.filter(key =>
        !APIPermissions.some(p => p.key === key)
      );

      if (invalidKeys.length > 0) {
        return jsonResponse(
          `Invalid permission keys: ${invalidKeys.join(', ')}`,
          HttpStatusCode.BadRequest
        );
      }

      const feature = await subscriptionFeatureService.createFeature(featureData);
      return jsonResponse(feature, HttpStatusCode.Created);

    } catch (error) {
      logging.logError('Error creating subscription feature', error);
      return jsonResponse(
        'Failed to create subscription feature',
        HttpStatusCode.InternalServerError
      );
    }
  }

  async getFeatures(req) {
    try {
      const module = req.query.get('module');
      const category = req.query.get('category');
      const isActive = req.query.get('isActive');
      const pageSize = parseInt(req.query.get('pageSize')) || 20;
      const continuationToken = req.query.get('continuationToken');

      const result = await subscriptionFeatureService.getFeatures({
        module,
        category,
        isActive: isActive ? isActive === 'true' : undefined,
        pageSize,
        continuationToken
      });

      return jsonResponse(result, HttpStatusCode.Ok);

    } catch (error) {
      logging.logError('Error fetching subscription features', error);
      return jsonResponse(
        'Failed to fetch subscription features',
        HttpStatusCode.InternalServerError
      );
    }
  }
}
```

### Azure Functions Configuration

#### 1. Subscription Feature Functions
```javascript
// src/functions/subscription-features.js
const { app } = require('@azure/functions');
const subscriptionFeatureHandler = require('../handlers/subscription-feature-handler');

app.http('create-subscription-feature', {
  methods: ['POST'],
  route: 'subscription/features',
  authLevel: 'function',
  handler: subscriptionFeatureHandler.createFeature
});

app.http('get-subscription-features', {
  methods: ['GET'],
  route: 'subscription/features',
  authLevel: 'function',
  handler: subscriptionFeatureHandler.getFeatures
});

app.http('update-subscription-feature', {
  methods: ['PATCH'],
  route: 'subscription/features/{featureId}',
  authLevel: 'function',
  handler: subscriptionFeatureHandler.updateFeature
});
```

#### 2. Subscription Plan Functions
```javascript
// src/functions/subscription-plans.js
const { app } = require('@azure/functions');
const subscriptionPlanHandler = require('../handlers/subscription-plan-handler');

app.http('create-subscription-plan', {
  methods: ['POST'],
  route: 'subscription/plans',
  authLevel: 'function',
  handler: subscriptionPlanHandler.createPlan
});

app.http('get-subscription-plans', {
  methods: ['GET'],
  route: 'subscription/plans',
  authLevel: 'function',
  handler: subscriptionPlanHandler.getPlans
});

app.http('assign-organization-subscription', {
  methods: ['POST'],
  route: 'subscription/organization/assign',
  authLevel: 'function',
  handler: subscriptionPlanHandler.assignToOrganization
});
```

### Permission Integration Implementation

#### Enhanced Permission Checking
```javascript
// src/utils/subscription-permission-utils.js
class SubscriptionPermissionUtils {
  static async getUserEffectivePermissions(userId) {
    const user = await userService.getUserById(userId);

    if (!user) {
      return [];
    }

    // Check if user has individual subscription
    if (user.subscriptionId) {
      const subscription = await userSubscriptionService.getSubscription(user.subscriptionId);
      if (subscription && subscription.status === 'ACTIVE') {
        const plan = await subscriptionPlanService.getPlanById(subscription.planId);
        return await this.getPermissionsFromPlan(plan);
      }
    }

    // Check organization subscription
    if (user.organizationId) {
      const org = await organizationService.getOrganizationById(user.organizationId);
      if (org && org.subscriptionId) {
        const subscription = await organizationSubscriptionService.getSubscription(org.subscriptionId);
        if (subscription && subscription.status === 'ACTIVE') {
          const plan = await subscriptionPlanService.getPlanById(subscription.planId);
          return await this.getPermissionsFromPlan(plan);
        }
      }
    }

    // Fallback to role-based permissions
    return await UserPermissionUtils.getUserPermissions(userId);
  }

  static async getPermissionsFromPlan(plan) {
    const allFeatureIds = [...plan.baseFeatures, ...plan.addonFeatures];
    const features = await subscriptionFeatureService.getFeaturesByIds(allFeatureIds);

    const permissionKeys = new Set();
    features.forEach(feature => {
      feature.permissionKeys.forEach(key => permissionKeys.add(key));
    });

    return Array.from(permissionKeys);
  }
}
```

### Email Service Extensions

#### Quote Request Email Implementation
```javascript
// src/services/quote-service.js
class QuoteService {
  async submitQuoteRequest(quoteData) {
    try {
      // Validate quote request data
      const requiredFields = ['organizationName', 'contactPerson', 'email', 'requirements'];
      const missingFields = requiredFields.filter(field => !quoteData[field]);

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      // Send email to sales team
      await this.sendQuoteRequestEmail(quoteData);

      // Store quote request for tracking
      const quoteRequest = {
        id: uuidv4(),
        ...quoteData,
        status: 'SUBMITTED',
        submittedAt: new Date().toISOString()
      };

      await cosmosDbContext.createItem(quoteRequest, 'quote_requests');

      return {
        success: true,
        message: 'Quote request submitted successfully',
        requestId: quoteRequest.id
      };

    } catch (error) {
      logging.logError('Error submitting quote request', error);
      throw error;
    }
  }

  async sendQuoteRequestEmail(quoteData) {
    const salesEmail = process.env.SALES_TEAM_EMAIL || '<EMAIL>';

    const subject = `New Quote Request from ${quoteData.organizationName}`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #4CAF50;">New Quote Request</h2>

        <div style="background-color: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Organization Details</h3>
          <p><strong>Organization Name:</strong> ${quoteData.organizationName}</p>
          <p><strong>Contact Person:</strong> ${quoteData.contactPerson}</p>
          <p><strong>Email:</strong> ${quoteData.email}</p>
          <p><strong>Phone:</strong> ${quoteData.phone || 'Not provided'}</p>
        </div>

        <div style="background-color: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Requirements</h3>
          <p><strong>Estimated Users:</strong> ${quoteData.estimatedUsers || 'Not specified'}</p>
          <p><strong>Required Modules:</strong> ${quoteData.modules ? quoteData.modules.join(', ') : 'Not specified'}</p>
          <p><strong>Detailed Requirements:</strong></p>
          <p style="background-color: white; padding: 15px; border-radius: 4px;">${quoteData.requirements}</p>
        </div>

        <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Submitted At:</strong> ${new Date().toLocaleString()}</p>
          <p><strong>Request ID:</strong> ${uuidv4()}</p>
        </div>

        <p style="margin-top: 30px; color: #666;">
          Please respond to this quote request within 24 hours.
        </p>
      </div>
    `;

    await emailService.sendEmail(salesEmail, subject, '', html);
  }
}
```

### Testing Strategy

#### Unit Tests Structure
```javascript
// tests/services/subscription-feature-service.test.js
describe('SubscriptionFeatureService', () => {
  describe('createFeature', () => {
    it('should create feature with valid data', async () => {
      const featureData = {
        key: 'emr.test.feature',
        name: 'Test Feature',
        module: 'EMR',
        category: 'BASE',
        permissionKeys: ['emr.patientinfo.view']
      };

      const result = await subscriptionFeatureService.createFeature(featureData);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.key).toBe(featureData.key);
    });

    it('should throw error for invalid permission keys', async () => {
      const featureData = {
        key: 'emr.test.feature',
        name: 'Test Feature',
        module: 'EMR',
        category: 'BASE',
        permissionKeys: ['invalid.permission.key']
      };

      await expect(subscriptionFeatureService.createFeature(featureData))
        .rejects.toThrow('Invalid permission keys');
    });
  });
});
```

#### Integration Tests
```javascript
// tests/integration/subscription-flow.test.js
describe('Subscription Integration Flow', () => {
  it('should complete user subscription flow', async () => {
    // 1. Create subscription plan
    const plan = await subscriptionPlanService.createPlan(testPlanData);

    // 2. Create payment order
    const paymentOrder = await paymentService.createOrder(testPaymentData);

    // 3. Simulate successful payment
    const paymentVerification = await paymentService.verifyPayment(testVerificationData);

    // 4. Create user subscription
    const subscription = await userSubscriptionService.createUserSubscription(
      plan.id,
      testUserData,
      paymentVerification
    );

    // 5. Verify user permissions
    const permissions = await SubscriptionPermissionUtils.getUserEffectivePermissions(subscription.userId);

    expect(subscription).toBeDefined();
    expect(permissions).toContain('emr.patientinfo.view');
  });
});
```

---

**Document Version**: 1.0
**Last Updated**: October 27, 2025
**Prepared By**: EMR Development Team

# EMR Subscription Feature Mapping

## 📋 Overview
This document maps the current permission system to subscription features, categorizing them into base features and add-ons for both EMR and MRD modules.

## 🏗️ Feature Categorization Strategy

### Base Features
Essential features that form the core functionality of each module. These are included in all subscription plans.

### Add-on Features  
Advanced or specialized features that can be added to enhance the base functionality. These are optional and can be purchased separately.

## 📊 EMR Module Features

### EMR Base Features

#### 1. Patient Information Management
**Feature ID**: `emr-patient-basic`  
**Name**: Patient Information Management  
**Description**: Core patient demographic and contact information management  
**Permission Keys**:
- `emr.patientinfo.view`
- `emr.patientinfo.edit`
- `emr.patientinfo.search`

#### 2. Basic Consultation
**Feature ID**: `emr-consultation-basic`  
**Name**: Basic Consultation Management  
**Description**: Schedule and manage basic patient consultations  
**Permission Keys**:
- `emr.consultation.view`
- `emr.consultation.create`
- `emr.consultation.edit`

#### 3. Doctor Profile Management
**Feature ID**: `emr-doctor-profile`  
**Name**: Doctor Profile Management  
**Description**: Manage doctor profiles and basic information  
**Permission Keys**:
- `emr.doctorprofile.view`
- `emr.doctorprofile.edit`

#### 4. Basic Prescription Management
**Feature ID**: `emr-prescription-basic`  
**Name**: Basic Prescription Management  
**Description**: Create and view basic prescriptions  
**Permission Keys**:
- `emr.prescription.view`
- `emr.prescription.manage`

#### 5. Basic Lab Test Management
**Feature ID**: `emr-labtest-basic`  
**Name**: Basic Lab Test Management  
**Description**: View and manage basic lab tests  
**Permission Keys**:
- `emr.lab-test.view`
- `emr.lab-test.manage`

#### 6. EMR Dashboard Access
**Feature ID**: `emr-dashboard-access`  
**Name**: EMR Dashboard Access  
**Description**: Access to EMR module and basic dashboard  
**Permission Keys**:
- `emr.access`
- `emr.dashboard.view`

### EMR Add-on Features

#### 1. Advanced Patient Data Access
**Feature ID**: `emr-patient-advanced`  
**Name**: Advanced Patient Data Access  
**Description**: Access to sensitive patient information including Aadhaar and ABHA  
**Permission Keys**:
- `emr.patientinfo.view.sensitive`
- `emr.patientinfo.view.aadhar`
- `emr.patientinfo.view.abha`

#### 2. Advanced Consultation Features
**Feature ID**: `emr-consultation-advanced`  
**Name**: Advanced Consultation Management  
**Description**: Future consultation scheduling and advanced consultation features  
**Permission Keys**:
- `emr.consultation.future.view`
- `emr.consultation.manage`

#### 3. Lab Report Management
**Feature ID**: `emr-reports-management`  
**Name**: Lab Report Management  
**Description**: Upload, process, and manage lab reports with OCR  
**Permission Keys**:
- `emr.reports.manage`

#### 4. Advanced Lab Testing
**Feature ID**: `emr-labtest-advanced`  
**Name**: Advanced Lab Test Management  
**Description**: Advanced lab test search and package management  
**Permission Keys**:
- `emr.lab-test.search`
- `emr.test-package.view`
- `emr.test-package.manage`

#### 5. Medicine Package Management
**Feature ID**: `emr-medicine-packages`  
**Name**: Medicine Package Management  
**Description**: Create and manage medicine packages and prescriptions  
**Permission Keys**:
- `emr.medicine-package.view`
- `emr.medicine-package.manage`
- `emr.prescription-package.view`
- `emr.prescription-package.manage`

#### 6. Lifestyle Management
**Feature ID**: `emr-lifestyle-management`  
**Name**: Lifestyle & Physical Activity Management  
**Description**: Track and manage patient lifestyle and physical activity data  
**Permission Keys**:
- `emr.lifestyle.manage`
- `emr.lifestyle.physical-activity.view`

#### 7. Payment Processing
**Feature ID**: `emr-payment-processing`  
**Name**: Payment Processing  
**Description**: Process payments for consultations, prescriptions, and lab tests  
**Permission Keys**:
- `emr.payment.appointment_booking`
- `emr.payment.lab_test`
- `emr.payment.prescription`

## 🏥 MRD Module Features

### MRD Base Features

#### 1. Patient Administrative Management
**Feature ID**: `mrd-patient-admin`  
**Name**: Patient Administrative Management  
**Description**: Basic patient administrative data management  
**Permission Keys**:
- `mrd.manage-patient.view`
- `mrd.manage-patient.edit`

#### 2. Patient Queue Management
**Feature ID**: `mrd-patient-queue`  
**Name**: Patient Queue Management  
**Description**: Manage patient queues and workflow  
**Permission Keys**:
- `mrd.patient-queue.manage`

#### 3. MRD Dashboard Access
**Feature ID**: `mrd-dashboard-access`  
**Name**: MRD Dashboard Access  
**Description**: Access to MRD module and dashboard  
**Permission Keys**:
- `mrd.access`
- `mrd.dashboard.view`

### MRD Add-on Features

#### 1. Patient Registration Payment
**Feature ID**: `mrd-payment-registration`  
**Name**: Patient Registration Payment Processing  
**Description**: Process payments for patient registration  
**Permission Keys**:
- `mrd.payment.patient_registration`

## 🔧 Administrative Features

### Core Administrative Features (Organization Super Admin Only)

#### 1. User Management
**Feature ID**: `admin-user-management`  
**Name**: User Management  
**Description**: Create, edit, and manage users within the organization  
**Permission Keys**:
- `user.view`
- `user.manage`

#### 2. Role Management
**Feature ID**: `admin-role-management`  
**Name**: Role & Permission Management  
**Description**: Manage roles and assign permissions  
**Permission Keys**:
- `role.manage`
- `permission.manage`

#### 3. Organization Management
**Feature ID**: `admin-organization-management`  
**Name**: Organization Management  
**Description**: Manage organization settings and view organization data  
**Permission Keys**:
- `organization.manage`
- `organization.patients.view`

#### 4. Dashboard Access
**Feature ID**: `admin-dashboard-access`  
**Name**: Administrative Dashboard  
**Description**: Access to administrative dashboard and analytics  
**Permission Keys**:
- `dashboard.view`

#### 5. Payment Management
**Feature ID**: `admin-payment-management`  
**Name**: Payment Management  
**Description**: View and manage payment transactions  
**Permission Keys**:
- `payment.view`
- `payment.webhook`

## 📦 Suggested Subscription Plans

### Starter Plan (EMR Basic)
**Target**: Small clinics (1-5 users)  
**Monthly Price**: ₹2,000  
**Features**:
- EMR Base Features: Patient Info, Basic Consultation, Doctor Profile, Basic Prescription, Basic Lab Tests, EMR Dashboard
- Administrative Features: User Management, Dashboard Access

### Professional Plan (EMR + MRD)
**Target**: Medium clinics/hospitals (5-25 users)  
**Monthly Price**: ₹5,000  
**Features**:
- All Starter Plan features
- MRD Base Features: Patient Admin, Patient Queue, MRD Dashboard
- EMR Add-ons: Advanced Patient Data, Medicine Packages
- Administrative Features: Role Management

### Enterprise Plan (Full Featured)
**Target**: Large hospitals (25+ users)  
**Monthly Price**: ₹10,000  
**Features**:
- All Professional Plan features
- All EMR Add-ons: Advanced Consultation, Lab Reports, Advanced Lab Testing, Lifestyle Management, Payment Processing
- All MRD Add-ons: Registration Payment Processing
- Full Administrative Features: Organization Management, Payment Management

### Individual User Plan
**Target**: Independent practitioners  
**Monthly Price**: ₹500  
**Features**:
- EMR Base Features (limited to single user)
- Basic Consultation and Prescription management
- Patient Information Management (up to 100 patients)

## 🔄 Migration Strategy

### Existing Organizations
1. **Assessment Phase**: Analyze current permission usage for each organization
2. **Plan Recommendation**: Suggest appropriate subscription plan based on current usage
3. **Migration**: Create "Legacy Full Access" plan with all current permissions
4. **Gradual Transition**: Allow organizations to choose appropriate plans over time

### Permission Mapping Process
```javascript
// Example migration script
const migrateOrganizationToSubscription = async (organizationId) => {
  // 1. Get current organization permissions
  const currentPermissions = await getCurrentOrgPermissions(organizationId);
  
  // 2. Find matching subscription plan
  const recommendedPlan = await findBestMatchingPlan(currentPermissions);
  
  // 3. Create organization subscription
  const subscription = await createOrganizationSubscription({
    organizationId,
    planId: recommendedPlan.id,
    status: 'ACTIVE',
    billingCycle: 'MONTHLY'
  });
  
  // 4. Update organization record
  await updateOrganization(organizationId, {
    subscriptionId: subscription.id,
    subscriptionStatus: 'ACTIVE'
  });
};
```

## 📊 Feature Usage Analytics

### Tracking Requirements
- **Feature Usage**: Track which features are actively used by each organization
- **User Engagement**: Monitor user interaction with different features
- **Performance Metrics**: Measure feature performance and adoption rates
- **Revenue Impact**: Analyze revenue generated by each feature/plan

### Implementation
```javascript
// Feature usage tracking
const trackFeatureUsage = async (userId, featureKey, action) => {
  await cosmosDbContext.createItem({
    id: uuidv4(),
    userId,
    featureKey,
    action, // 'access', 'create', 'edit', 'delete'
    timestamp: new Date().toISOString(),
    organizationId: user.organizationId
  }, 'feature_usage_analytics');
};
```

---

**Document Version**: 1.0  
**Last Updated**: October 27, 2025  
**Prepared By**: EMR Development Team

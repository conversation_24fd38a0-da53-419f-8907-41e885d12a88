const lifeStyleService = require('../services/lifestyle-service')
const logging = require('../common/logging')
const helper = require('../common/helper')
const axios = require('axios')
const openAIService = require('../services/openai-service')

class LifestyleAmbientHandler {
  async identifySpeaker(transcript) {
    try {
      var classification =
        'You are an AI assistant that helps doctors separate chat transcripts to identify which parts are spoken by the doctor and which by the patient.\n\n' +
        'IMPORTANT: Return ONLY valid JSON in the exact format below. Do not include any markdown, explanations, or other text.\n\n' +
        'Format: [{"speaker": "doctor", "message": "exact message text"},{"speaker": "patient", "message": "exact message text"}]\n\n' +
        'Rules:\n' +
        '1. Each message must be a complete sentence or thought\n' +
        '2. Escape all quotes inside message content using \\" \n' +
        '3. Do not break messages in the middle of sentences\n' +
        '4. Combine into 1 inline JSON without newlines\n' +
        '5. If no clear speaker identification is possible, use "patient" as default\n' +
        '6. Ensure all JSON brackets and braces are properly closed\n\n' +
        'Return only the JSON array, nothing else.'
      var result = await openAIService.chatCompletion(
        classification,
        transcript,
      )
      return result
    } catch (error) {
      logging.logError('Unable to identify Speaker', error)
      return null
    }
  }

  /**
   * Process a field to ensure it has proper default values
   * @param {Object} field - Field object from database
   * @returns {Object} Processed field with default values
   */
  processFieldWithDefaults(field) {
    const processedField = { ...field }

    // Ensure all fields have a value property with appropriate defaults
    if (!processedField.hasOwnProperty('value')) {
      switch (field.type) {
        case 'table':
          processedField.value = []
          break
        case 'grouped_table':
          // Grouped table with meal groups structure
          if (field.mealGroups && Array.isArray(field.mealGroups)) {
            processedField.value = field.mealGroups.map((group) => ({
              id: group.id,
              label: group.label,
              rows: group.defaultRows || [],
            }))
          } else {
            processedField.value = []
          }
          break
        case 'field_group':
          // Field group with nested fields
          if (field.fields && Array.isArray(field.fields)) {
            const groupValue = {}
            field.fields.forEach((subField) => {
              groupValue[subField.id] = ''
            })
            processedField.value = groupValue
          } else {
            processedField.value = {}
          }
          break
        case 'frequency':
          // Frequency fields for food frequency questionnaire
          processedField.value = ''
          break
        case 'section':
          // Section fields have nested fields that also need default values
          if (field.fields && Array.isArray(field.fields)) {
            processedField.value = {
              fields: field.fields.map(() => ({ value: '' })),
            }
          } else {
            processedField.value = { fields: [] }
          }
          break
        case 'conditional':
          // Conditional fields need empty value and subField handling
          processedField.value = ''
          if (field.conditions && Array.isArray(field.conditions)) {
            processedField.conditions = field.conditions.map((condition) => ({
              ...condition,
              subField: condition.subField
                ? {
                    ...condition.subField,
                    value: condition.subField.value || '',
                  }
                : undefined,
            }))
          }
          break
        case 'radio':
        case 'select':
        case 'conditional_select':
        case 'text':
        case 'number':
        case 'time_range':
        case 'searchable_select':
        case 'dependent_autofill':
        case 'icon':
        case 'slider':
        default:
          processedField.value = ''
          break
      }
    }

    // Process nested fields in sections and field_groups
    if (
      (field.type === 'section' || field.type === 'field_group') &&
      field.fields &&
      Array.isArray(field.fields)
    ) {
      processedField.fields = field.fields.map((subField) =>
        this.processFieldWithDefaults(subField),
      )
    }

    return processedField
  }

  async processLifestyleQuestions(conversation, source) {
    try {
      const questionsData = await lifeStyleService.getLifeStyeBySourceName(
        source,
      )

      if (!questionsData || !Array.isArray(questionsData)) {
        logging.logError(`No questions found for source: ${source}`)
        return { questions: [] }
      }

      const questions = []
      for (const questionDoc of questionsData) {
        if (questionDoc.questions && Array.isArray(questionDoc.questions)) {
          const processedQuestions = questionDoc.questions.map((question) => {
            const processedQuestion = {
              ...question,
              fields: question.fields
                ? question.fields.map((field) =>
                    this.processFieldWithDefaults(field),
                  )
                : [],
            }

            if (
              question.sections &&
              Array.isArray(question.sections) &&
              question.sections.length > 0
            ) {
              processedQuestion.sections = question.sections.map((section) => ({
                ...section,
                fields: section.fields
                  ? section.fields.map((field) =>
                      this.processFieldWithDefaults(field),
                    )
                  : [],
              }))
            }

            return processedQuestion
          })

          questions.push(...processedQuestions)
        }
      }

      if (questions.length === 0) {
        logging.logError(`No questions structure found for source: ${source}`)
        return { questions: [] }
      }

      logging.logInfo(
        `Extracted ${questions.length} questions from database for processing`,
      )

      // const serviceUrl =
      //'http://arcaquest-ehr-v1.eastus.azurecontainer.io:8000/process-ehr-json/'
      const baseUrl = process.env.ARCAQUEST_EHR_BASE_URL
      if (!baseUrl) {
        logging.logError('Missing ARCAQUEST_EHR_BASE_URL env var')
        return { questions: [] }
      }
      const serviceUrl = new URL('/process-ehr-json/', baseUrl).toString()
      const payload = {
        conversation: conversation,
        summary: {
          questions: questions,
        },
      }

      const response = await axios.post(serviceUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 300000, // 5 minutes timeout
      })

      if (
        !response.data ||
        !response.data.summary ||
        !response.data.summary.questions
      ) {
        logging.logError('Invalid response from external service')
        return { questions: [] }
      }

      logging.logInfo(
        'External service response:',
        JSON.stringify(response.data, null, 2),
      )

      // Debug: Check if fields have values
      response.data.summary.questions.forEach((question, qIndex) => {
        logging.logInfo(`Question ${qIndex} (${question.id}):`)
        if (question.fields && Array.isArray(question.fields)) {
          question.fields.forEach((field, fIndex) => {
            logging.logInfo(
              `  Field ${fIndex} (${field.id}): ${
                field.label
              } = ${JSON.stringify(field.value)}`,
            )
          })
        }
        if (question.sections && Array.isArray(question.sections)) {
          question.sections.forEach((section, sIndex) => {
            logging.logInfo(`  Section ${sIndex} (${section.id}):`)
            if (section.fields && Array.isArray(section.fields)) {
              section.fields.forEach((field, fIndex) => {
                logging.logInfo(
                  `    Field ${fIndex} (${field.id}): ${
                    field.label
                  } = ${JSON.stringify(field.value)}`,
                )
              })
            }
          })
        }
      })

      logging.logInfo(
        'Successfully processed lifestyle questions via external service',
      )
      return { questions: response.data.summary.questions }
    } catch (error) {
      logging.logError('Unable to process lifestyle questions', error)

      // Log more details about the error
      if (error.response) {
        logging.logError('External service error response:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
        })
      } else if (error.request) {
        logging.logError('External service request error:', error.request)
      } else {
        logging.logError('External service error:', error.message)
      }

      return { questions: [] }
    }
  }

  async processLifestyleAmbientListening(transcript, source) {
    try {
      const conversation = await this.identifySpeaker(transcript)
      const objConversation = helper.parseJSON(conversation)

      if (!objConversation || !Array.isArray(objConversation)) {
        logging.logError('Failed to parse conversation')
        return {
          conversation: [],
          summary: {},
        }
      }

      const summary = await this.processLifestyleQuestions(
        objConversation,
        source,
      )

      return {
        conversation: objConversation,
        summary: summary,
      }
    } catch (error) {
      logging.logError('Unable to process lifestyle ambient listening', error)
      return {
        conversation: [],
        summary: {},
      }
    }
  }
}

module.exports = new LifestyleAmbientHandler()

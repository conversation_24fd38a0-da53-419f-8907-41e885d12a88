const { app } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const summaryHandler = require('../handlers/summary-handler')
const helper = require('../common/helper')

app.http('summary', {
  methods: ['POST'],
  authLevel: 'anonymous',
  handler: async (req, context) => {
    context.log(`Http function processed req for url "${req.url}"`)

    try {
      const transcript = await req.text()
      if (!transcript || transcript == '') {
        return jsonResponse(`Missing transcript`, HttpStatusCode.BadRequest)
      }

      context.log(`Processing transcript of length: ${transcript.length}`)

      const conversation = await summaryHandler.identifySpeaker(transcript)
      if (!conversation) {
        context.log('Failed to identify speakers, using fallback')
        return jsonResponse({
          conversation: [{ speaker: 'patient', message: transcript }],
          summary: helper.getDefaultSummary(),
        })
      }

      context.log(`Raw conversation response length: ${conversation.length}`)

      // Parse conversation with enhanced error handling first
      let objConversation = helper.parseJSON(conversation)
      if (
        !objConversation ||
        objConversation === '' ||
        !Array.isArray(objConversation)
      ) {
        context.log('Failed to parse conversation JSON, using fallback')
        objConversation = [{ speaker: 'patient', message: transcript }]
      }

      context.log(`Parsed conversation with ${objConversation.length} messages`)

      // Generate summary using the parsed conversation array
      const summary = await summaryHandler.sumnmaryConversation(
        JSON.stringify(objConversation),
      )
      if (!summary) {
        context.log('Failed to generate summary, using default')
      }

      // Parse summary with fallback
      let objsummary = helper.parseJSON(summary)
      if (!objsummary || objsummary === '') {
        context.log('Failed to parse summary JSON, using default')
        objsummary = null
      }

      if (!objsummary || !helper.validateMedicalRecord(objsummary)) {
        context.log('Summary validation failed, using default structure')
        objsummary = helper.getDefaultSummary()
      }

      return jsonResponse({
        conversation: objConversation,
        summary: objsummary,
      })
    } catch (error) {
      context.log('Error in summary function:', error)
      return jsonResponse(
        {
          error: 'Internal server error processing summary',
          details: error.message,
        },
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

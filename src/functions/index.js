const { app } = require('@azure/functions')
const AuthMessage = require('../common/auth-message')
const { doValidate } = require('../common/user-validation')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const finalizePatientRecordsCron = require('../tasks/finalize-patient-history-cron')


finalizePatientRecordsCron()
app.setup({
  enableHttpStream: false,
})

app.hook.appStart(async (context) => {
  console.log('EMR Function start...')
  app.setup({
    enableHttpStream: false,
  })
})

app.hook.appTerminate((context) => {
  console.log('EMR Function stop...')
})

app.hook.preInvocation(async (context) => {
  var req = context.inputs[0]
  // Skip auth for non-HTTP functions (like timers)
  if (!req || !req.url) {
    return;
  }
  const auth = await doValidate(req)
  if (auth.message != AuthMessage.SUCCESS) {
    context.functionHandler = (...args) => {
      return jsonResponse(auth.message, HttpStatusCode.Unauthorized)
    }
  } else {
    context.invocationContext.extraInputs.set('decode', auth.decode)
  }
})
